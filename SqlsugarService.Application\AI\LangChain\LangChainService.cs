using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using SqlsugarService.Application.AI.LangChain.Dtos;
using System.Diagnostics;
using System.Text.Json;

namespace SqlsugarService.Application.AI.LangChain
{
    /// <summary>
    /// LangChain服务实现
    /// 
    /// 基于LangChain.NET库实现的智能对话服务，提供以下功能：
    /// - 基础对话功能 - 与大语言模型进行交互
    /// - 对话历史记忆 - 保持上下文连贯性
    /// - 链式工具调用 - 支持复杂的多步骤任务
    /// - 自定义提示词模板 - 控制AI的行为和输出
    /// </summary>
    public class LangChainService : ILangChainService
    {
        #region 私有字段

        /// <summary>
        /// 配置服务
        /// </summary>
        private readonly IConfiguration _configuration;

        /// <summary>
        /// 日志记录器
        /// </summary>
        private readonly ILogger<LangChainService> _logger;

        /// <summary>
        /// Semantic Kernel 实例
        /// </summary>
        private readonly Kernel _kernel;

        /// <summary>
        /// 聊天完成服务
        /// </summary>
        private readonly IChatCompletionService _chatCompletionService;

        /// <summary>
        /// 会话记忆管理器
        /// 用于存储不同用户的对话历史
        /// </summary>
        private readonly Dictionary<string, ChatHistory> _memoryStore = new();

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="configuration">配置服务</param>
        /// <param name="logger">日志记录器</param>
        public LangChainService(
            IConfiguration configuration,
            ILogger<LangChainService> logger)
        {
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            // 创建 Kernel Builder
            var builder = Kernel.CreateBuilder();

            // 获取配置
            var apiKey = _configuration["LangChain:ApiKey"] ?? throw new InvalidOperationException("未配置LangChain:ApiKey");
            var endpoint = _configuration["LangChain:ApiBaseUrl"];

            // 根据配置添加聊天完成服务
            if (!string.IsNullOrEmpty(endpoint) && endpoint.Contains("azure"))
            {
                // Azure OpenAI
                var deploymentName = _configuration["LangChain:ModelName"] ?? "gpt-3.5-turbo";
                builder.AddAzureOpenAIChatCompletion(deploymentName, endpoint, apiKey);
            }
            else
            {
                // OpenAI
                builder.AddOpenAIChatCompletion(_configuration["LangChain:ModelName"] ?? "gpt-3.5-turbo", apiKey);
            }

            _kernel = builder.Build();
            _chatCompletionService = _kernel.GetRequiredService<IChatCompletionService>();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 发送消息到Semantic Kernel并获取回复
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns>AI回复内容</returns>
        public async Task<LangChainResponseDto> SendMessageAsync(LangChainRequestDto request)
        {
            var stopwatch = Stopwatch.StartNew();
            var response = new LangChainResponseDto();

            try
            {
                _logger.LogInformation("开始处理Semantic Kernel消息，内容长度: {Length}", request.Content?.Length ?? 0);

                // 验证请求
                if (string.IsNullOrWhiteSpace(request.Content))
                {
                    throw new ArgumentException("消息内容不能为空", nameof(request.Content));
                }

                // 获取配置
                var modelName = request.ModelName ?? _configuration["LangChain:ModelName"] ?? "gpt-3.5-turbo";
                var temperature = request.Temperature ?? float.Parse(_configuration["LangChain:Temperature"] ?? "0.7");
                var systemPrompt = request.SystemPrompt ?? _configuration["LangChain:SystemPrompt"] ?? "你是一个有用的AI助手。";

                // 生成会话ID
                var sessionId = request.SessionId ?? Guid.NewGuid().ToString();
                var memoryKey = $"{request.UserId}:{sessionId}";

                // 获取或创建聊天历史
                var chatHistory = GetOrCreateChatHistory(memoryKey, request.EnableMemory);

                // 添加系统消息（如果聊天历史为空）
                if (chatHistory.Count == 0)
                {
                    chatHistory.AddSystemMessage(systemPrompt);
                }

                // 添加用户消息
                chatHistory.AddUserMessage(request.Content);

                // 创建执行设置
                var executionSettings = new OpenAIPromptExecutionSettings
                {
                    Temperature = temperature,
                    ModelId = modelName
                };

                // 发送消息并获取回复
                var result = await _chatCompletionService.GetChatMessageContentAsync(chatHistory, executionSettings);

                // 如果启用记忆，保存AI回复到聊天历史
                if (request.EnableMemory)
                {
                    chatHistory.AddAssistantMessage(result.Content ?? string.Empty);
                }

                // 构建响应
                response.Success = true;
                response.Content = result.Content;
                response.SessionId = sessionId;
                response.ModelName = modelName;
                response.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;

                // 设置令牌使用情况（如果可用）
                if (result.Metadata?.TryGetValue("Usage", out var usageObj) == true && usageObj is Dictionary<string, object> usage)
                {
                    response.TokenUsage = new TokenUsage
                    {
                        PromptTokens = usage.TryGetValue("PromptTokens", out var promptTokens) ? Convert.ToInt32(promptTokens) : 0,
                        CompletionTokens = usage.TryGetValue("CompletionTokens", out var completionTokens) ? Convert.ToInt32(completionTokens) : 0,
                        TotalTokens = usage.TryGetValue("TotalTokens", out var totalTokens) ? Convert.ToInt32(totalTokens) : 0
                    };
                }

                _logger.LogInformation("Semantic Kernel消息处理成功，耗时: {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
                return response;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Semantic Kernel消息处理失败: {Message}", ex.Message);

                response.Success = false;
                response.ErrorMessage = $"处理失败: {ex.Message}";
                response.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;
                return response;
            }
        }

        /// <summary>
        /// 快速发送消息（使用默认配置）
        /// </summary>
        /// <param name="content">消息内容</param>
        /// <param name="userId">用户ID，可选</param>
        /// <returns>AI回复内容</returns>
        public async Task<LangChainResponseDto> QuickSendAsync(string content, string? userId = null)
        {
            var request = new LangChainRequestDto
            {
                Content = content,
                UserId = userId ?? "default_user",
                EnableMemory = true
            };

            return await SendMessageAsync(request);
        }

        /// <summary>
        /// 清除指定用户的会话历史
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="sessionId">会话ID，如果为null则清除该用户的所有会话</param>
        /// <returns>操作是否成功</returns>
        public Task<bool> ClearMemoryAsync(string userId, string? sessionId = null)
        {
            try
            {
                if (string.IsNullOrEmpty(sessionId))
                {
                    // 清除用户的所有会话
                    var keysToRemove = _memoryStore.Keys
                        .Where(k => k.StartsWith($"{userId}:"))
                        .ToList();

                    foreach (var key in keysToRemove)
                    {
                        _memoryStore.Remove(key);
                    }

                    _logger.LogInformation("已清除用户 {UserId} 的所有会话历史，共 {Count} 个", userId, keysToRemove.Count);
                }
                else
                {
                    // 清除特定会话
                    var memoryKey = $"{userId}:{sessionId}";
                    var removed = _memoryStore.Remove(memoryKey);
                    _logger.LogInformation("已{Result}清除用户 {UserId} 的会话 {SessionId} 历史",
                        removed ? "" : "尝试但未找到要", userId, sessionId);
                }

                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清除会话历史失败: {Message}", ex.Message);
                return Task.FromResult(false);
            }
        }

        /// <summary>
        /// 健康检查
        /// </summary>
        /// <returns>服务是否正常</returns>
        public Task<bool> HealthCheckAsync()
        {
            try
            {
                var apiKey = _configuration["LangChain:ApiKey"];
                if (string.IsNullOrEmpty(apiKey))
                {
                    _logger.LogWarning("LangChain健康检查失败: 未配置API密钥");
                    return Task.FromResult(false);
                }

                _logger.LogInformation("LangChain健康检查通过");
                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "LangChain健康检查失败: {Message}", ex.Message);
                return Task.FromResult(false);
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 获取或创建聊天历史
        /// </summary>
        /// <param name="memoryKey">记忆键</param>
        /// <param name="enableMemory">是否启用记忆</param>
        /// <returns>聊天历史对象</returns>
        private ChatHistory GetOrCreateChatHistory(string memoryKey, bool enableMemory)
        {
            if (!enableMemory)
            {
                return new ChatHistory();
            }

            if (!_memoryStore.TryGetValue(memoryKey, out var chatHistory))
            {
                chatHistory = new ChatHistory();
                _memoryStore[memoryKey] = chatHistory;
                _logger.LogDebug("为 {MemoryKey} 创建新的聊天历史", memoryKey);
            }

            return chatHistory;
        }

        /// <summary>
        /// 发送带工具定义的消息，支持AI调用工具
        /// </summary>
        /// <param name="request">带工具定义的请求参数</param>
        /// <returns>AI回复内容，可能包含工具调用</returns>
        public async Task<LangChainToolResponseDto> SendMessageWithToolsAsync(LangChainToolRequestDto request)
        {
            var stopwatch = Stopwatch.StartNew();
            var response = new LangChainToolResponseDto();

            try
            {
                _logger.LogInformation("开始处理带工具的Semantic Kernel消息，工具数量: {ToolCount}", request.Tools?.Count ?? 0);

                // 验证请求
                if (string.IsNullOrWhiteSpace(request.Content))
                {
                    throw new ArgumentException("消息内容不能为空", nameof(request.Content));
                }

                // 获取配置
                var modelName = request.ModelName ?? _configuration["LangChain:ModelName"] ?? "gpt-3.5-turbo";
                var temperature = request.Temperature ?? float.Parse(_configuration["LangChain:Temperature"] ?? "0.7");
                var systemPrompt = request.SystemPrompt ?? _configuration["LangChain:SystemPrompt"] ?? "你是一个MES系统的智能助手，可以帮助用户查询数据和执行操作。";

                // 生成会话ID
                var sessionId = request.SessionId ?? Guid.NewGuid().ToString();
                var memoryKey = $"{request.UserId}:{sessionId}";

                // 获取或创建聊天历史
                var chatHistory = GetOrCreateChatHistory(memoryKey, request.EnableMemory);

                // 添加系统消息（如果聊天历史为空）
                if (chatHistory.Count == 0)
                {
                    chatHistory.AddSystemMessage(systemPrompt);
                }

                // 添加用户消息
                chatHistory.AddUserMessage(request.Content);

                // 创建执行设置
                var executionSettings = new OpenAIPromptExecutionSettings
                {
                    Temperature = temperature,
                    ModelId = modelName,
                    // 注意：工具调用功能需要额外的配置，这里暂时提供基础实现
                    ToolCallBehavior = ToolCallBehavior.AutoInvokeKernelFunctions
                };

                // 发送消息并获取回复
                var result = await _chatCompletionService.GetChatMessageContentAsync(chatHistory, executionSettings);

                // 如果启用记忆，保存AI回复到聊天历史
                if (request.EnableMemory)
                {
                    chatHistory.AddAssistantMessage(result.Content ?? string.Empty);
                }

                response.Success = true;
                response.Content = result.Content;
                response.SessionId = sessionId;
                response.ModelName = modelName;
                response.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;
                response.ToolCalls = new List<ToolCall>(); // 暂时返回空列表，需要进一步实现工具调用解析
                response.RequiresAction = false;

                _logger.LogInformation("带工具的Semantic Kernel消息处理成功，耗时: {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);

                return response;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "带工具的Semantic Kernel消息处理失败: {Message}", ex.Message);

                response.Success = false;
                response.ErrorMessage = $"处理失败: {ex.Message}";
                response.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;
                return response;
            }
        }

        /// <summary>
        /// 提交工具执行结果并继续对话
        /// </summary>
        /// <param name="request">工具执行结果</param>
        /// <returns>AI的后续回复</returns>
        public async Task<LangChainToolResponseDto> SubmitToolResultsAsync(ToolResultsRequest request)
        {
            var stopwatch = Stopwatch.StartNew();
            var response = new LangChainToolResponseDto();

            try
            {
                _logger.LogInformation("提交工具执行结果，工具调用ID: {ToolCallId}", request.ToolCallId);

                // 验证请求
                if (string.IsNullOrWhiteSpace(request.ToolCallId))
                {
                    throw new ArgumentException("工具调用ID不能为空", nameof(request.ToolCallId));
                }

                if (string.IsNullOrWhiteSpace(request.Output))
                {
                    throw new ArgumentException("工具执行结果不能为空", nameof(request.Output));
                }

                // 获取配置
                var modelName = _configuration["LangChain:ModelName"] ?? "gpt-3.5-turbo";
                var temperature = float.Parse(_configuration["LangChain:Temperature"] ?? "0.7");
                var systemPrompt = _configuration["LangChain:SystemPrompt"] ?? "你是一个MES系统的智能助手。";

                // 获取聊天历史
                var memoryKey = $"{request.UserId}:{request.SessionId}";
                var chatHistory = GetOrCreateChatHistory(memoryKey, true);

                // 添加工具执行结果作为系统消息
                chatHistory.AddSystemMessage($"工具 {request.ToolCallId} 的执行结果: {request.Output}");

                // 创建执行设置
                var executionSettings = new OpenAIPromptExecutionSettings
                {
                    Temperature = temperature,
                    ModelId = modelName
                };

                // 发送消息并获取回复
                var result = await _chatCompletionService.GetChatMessageContentAsync(chatHistory, executionSettings);

                // 保存AI回复到聊天历史
                chatHistory.AddAssistantMessage(result.Content ?? string.Empty);

                response.Success = true;
                response.Content = result.Content;
                response.SessionId = request.SessionId;
                response.ModelName = modelName;
                response.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;
                response.ToolCalls = new List<ToolCall>(); // 暂时返回空列表
                response.RequiresAction = false;

                _logger.LogInformation("工具结果提交处理成功，耗时: {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
                return response;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "工具结果提交失败: {Message}", ex.Message);

                response.Success = false;
                response.ErrorMessage = $"处理失败: {ex.Message}";
                response.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;
                return response;
            }
        }

        #endregion
    }
}
