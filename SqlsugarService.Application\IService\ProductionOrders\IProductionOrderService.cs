﻿using SqlsugarService.Application.DTOs.ProductionOrderDto;
using SqlsugarService.Application.Until;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SqlsugarService.Application.IService.ProductionOrders
{
    public interface IProductionOrderService
    {
        /// <summary>
        /// 获取生产工单分页列表
        /// </summary>
        /// <param name="search">搜索条件</param>
        /// <returns>分页结果</returns>
        Task<ApiResult<PageResult<List<GetProductionOrderDto>>>> GetProductionOrderList(GetProductionOrderSearchDto search);
    }
}
