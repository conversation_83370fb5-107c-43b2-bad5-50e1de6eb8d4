﻿using SqlsugarService.Application.DTOs.ProductionOrderDto;
using SqlsugarService.Application.IService.ProductionOrders;
using SqlsugarService.Application.Until;
using SqlsugarService.Domain.Plan;
using SqlsugarService.Infrastructure.IRepository;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;

namespace SqlsugarService.Application.Service.ProductionOrders
{
    public class ProductionOrderService: IProductionOrderService
    {
        private readonly IBaseRepository<ProductionOrder> productionorderbase;
        private readonly IMapper _mapper;

        public ProductionOrderService(IBaseRepository<ProductionOrder> productionorderbase, IMapper mapper)
        {
            this.productionorderbase = productionorderbase;
            this._mapper = mapper;
        }
        
        /// <summary>
        /// 获取生产工单分页列表
        /// </summary>
        /// <param name="search">搜索条件</param>
        /// <returns>分页结果</returns>
        public async Task<ApiResult<PageResult<List<GetProductionOrderDto>>>> GetProductionOrderList(GetProductionOrderSearchDto search)
        { 
            try
            {
                // 获取所有生产工单数据
                var query = await productionorderbase.GetAllAsync();
                
                // 根据搜索条件进行过滤
                if (!string.IsNullOrEmpty(search.OrderNumber))
                {
                    query = query.Where(x => x.OrderNumber.Contains(search.OrderNumber) || 
                                           x.OrderName.Contains(search.OrderNumber)).ToList();
                }
                
                if (!string.IsNullOrEmpty(search.ProductName))
                {
                    query = query.Where(x => x.ProductName.Contains(search.ProductName) || 
                                           x.ProductNumber.Contains(search.ProductName)).ToList();
                }
                
                if (!string.IsNullOrEmpty(search.Status))
                {
                    query = query.Where(x => x.Status == search.Status).ToList();
                }

                // 计算总数和总页数
                var totalCount = query.Count();
                var totalPage = (int)Math.Ceiling(totalCount * 1.0 / search.PageSize);

                // 分页查询
                var productionOrders = query
                    .OrderByDescending(x => x.CreatedAt) // 按创建时间倒序排列
                    .Skip((search.PageIndex - 1) * search.PageSize)
                    .Take(search.PageSize)
                    .ToList();

                // 映射为DTO
                var result = _mapper.Map<List<GetProductionOrderDto>>(productionOrders);

                // 返回分页结果
                return ApiResult<PageResult<List<GetProductionOrderDto>>>.Success(
                    new PageResult<List<GetProductionOrderDto>>
                    {
                        TotalCount = totalCount,
                        TotalPage = totalPage,
                        Data = result
                    },
                    ResultCode.Success);
            }
            catch (Exception ex)
            {
                return ApiResult<PageResult<List<GetProductionOrderDto>>>.Fail($"获取生产工单列表失败：{ex.Message}", ResultCode.Error);
            }
        }

        /// <summary>
        /// 根据生产计划中的BOMid 分解添加工单数据信息
        /// </summary>
        //public async Task<ApiResult> InsertProductionOrder(InsertupdateproductionorderDto dto)
        //{ 

        //}


        /// <summary>
        /// 根据当前时间生成唯一的 ProductNumber，格式为 D + yyyyMMddHHmmss
        /// </summary>
        /// <returns></returns>
        private string GenerateProductNumberByTime()
        {
            var now = DateTime.Now;
            var timePart = now.ToString("yyyyMMddHHmmssfff");
            return $"GDBH{timePart}";
        }
    }
}
