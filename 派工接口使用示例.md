# 🔧 工单任务派工接口使用指南

## 📋 功能概述

新增的派工接口支持单个和批量派工操作，可以为工单任务分配班组、负责人、质检人员等信息。

## 🚀 API 接口

### 批量派工接口
```http
POST /api/WorkOrderTask/batch-dispatch
Content-Type: application/json
```

## 📝 请求参数

### 单个派工示例
```json
[
  {
    "workOrderTaskEntityId": "12345678-1234-1234-1234-123456789012",
    "teamName": "装配班组A",
    "teamprincipal": "张三",
    "otherMembers": "李四,王五,赵六",
    "qualityTestingDept": "质检部",
    "qualityTestingPeople": "质检员小明",
    "descr": "紧急任务，需要优先处理"
  }
]
```

### 批量派工示例
```json
[
  {
    "workOrderTaskEntityId": "12345678-1234-1234-1234-123456789012",
    "teamName": "装配班组A",
    "teamprincipal": "张三",
    "otherMembers": "李四,王五",
    "qualityTestingDept": "质检部",
    "qualityTestingPeople": "质检员小明",
    "descr": "装配任务"
  },
  {
    "workOrderTaskEntityId": "87654321-4321-4321-4321-210987654321",
    "teamName": "机加班组B",
    "teamprincipal": "赵六",
    "otherMembers": "钱七,孙八",
    "qualityTestingDept": "质检部",
    "qualityTestingPeople": "质检员小红",
    "descr": "机加工任务"
  }
]
```

## 📊 响应格式

### 成功响应
```json
{
  "isSuc": true,
  "message": "派工成功",
  "code": 200,
  "data": null
}
```

### 失败响应
```json
{
  "isSuc": false,
  "message": "工单任务 装配任务001 已经派工，不能重复派工",
  "code": 400,
  "data": null
}
```

## ⚠️ 注意事项

1. **必填字段验证**：
   - `workOrderTaskEntityId`：工单任务ID（必填）
   - `teamName`：班组名称（必填，最大100字符）
   - `teamprincipal`：负责人名称（必填，最大50字符）

2. **业务规则**：
   - 同一个工单任务只能派工一次，重复派工会返回错误
   - 工单任务ID必须存在，否则返回404错误
   - 支持单个和批量操作，传入数组即可

3. **字段长度限制**：
   - 班组名称：最大100字符
   - 负责人名称：最大50字符
   - 其他成员：最大500字符
   - 质检部门：最大100字符
   - 质检人员：最大100字符
   - 备注：最大1000字符

## 🔄 使用流程

1. 获取待派工的工单任务列表
2. 准备派工信息（班组、负责人等）
3. 调用批量派工接口
4. 处理返回结果

## 💡 最佳实践

- 建议在派工前先查询工单任务状态，确保任务可以派工
- 批量派工时，如果其中一个任务失败，整个批次都会回滚
- 派工信息中的人员名称建议从系统中的人员管理模块获取，确保数据一致性
