﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SqlsugarService.Application.DTOs.ProductionOrderDto;
using SqlsugarService.Application.IService.ProductionOrders;
using SqlsugarService.Application.Until;

namespace SqlsugarService.API.Controllers.ProductionOrders
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class ProductionOrderController : ControllerBase
    {
        private readonly IProductionOrderService productorderserservice;

        public ProductionOrderController(IProductionOrderService productorderserservice)
        {
            this.productorderserservice = productorderserservice;
        }
        /// <summary>
        /// 获取生产工单分页列表
        /// </summary>
        /// <param name="search"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ApiResult<PageResult<List<GetProductionOrderDto>>>> GetProductionOrderList([FromQuery]GetProductionOrderSearchDto search)
        {
            return await productorderserservice.GetProductionOrderList(search);
        }
    }
}
